{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAOA,OAAO,EAAE,OAAO,EAAmC,MAAM,WAAW,CAAA;AACpE,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAA;AAE3C,kDAAkD;AAClD,YAAY;AACZ,OAAO,KAAK,MAAM,eAAe,CAAA;AACjC,OAAO,EAAE,QAAQ,EAAE,MAAM,WAAW,CAAA;AAEpC,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CACpB,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,EAC3D,EAAE,CACH,CAAA;AAED,wCAAwC;AACxC,MAAM,MAAM,GAAG,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;AAEzC,MAAM,QAAQ,GAAG,CAAC,IAAY,EAAE,GAAW,EAAU,EAAE;IACrD,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,OAAO,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAC;SAC9C,IAAI,CAAC,GAAG,CAAC;SACT,IAAI,EAAE;SACN,WAAW,EAAE;SACb,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;AACvB,CAAC,CAAA;AAED,MAAM,QAAQ,GAAG,CACf,KAAkE,EAClE,QAAgB,IAAI,EACZ,EAAE;IACV,MAAM,GAAG,GACP,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK;QACjC,CAAC,CAAC,OAAO,KAAK,KAAK,SAAS,CAAC,CAAC;YAC5B,KAAK,CAAC,CAAC,CAAC,GAAG;gBACX,CAAC,CAAC,GAAG;YACP,CAAC,CAAC,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC3C,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;oBACtB,KAAK,CAAC,GAAG,CAAC,CAAC,CAA4B,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;oBACtE,CAAC,CAAC,qBAAqB,CAAC,SAAS,CAAA;IACnC,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;QAC5B,MAAM,IAAI,KAAK,CACb,6CAA6C,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CACrE,CAAA;IACH,CAAC;IACD,oBAAoB;IACpB,OAAO,GAAG,CAAA;AACZ,CAAC,CAAA;AAED,MAAM,UAAU,GAAG,CACjB,GAAW,EACX,IAAO,EACP,QAAW,EACX,QAAgB,IAAI,EACF,EAAE,CACpB,CAAC,QAAQ,CAAC,CAAC;IACT,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QAC3D,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG;QACzB,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG;YAClC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,CAAqB,CAAA;AA6HpC,MAAM,CAAC,MAAM,YAAY,GAAG,CAAC,CAAS,EAAmB,EAAE,CACzD,OAAO,CAAC,KAAK,QAAQ;IACrB,CAAC,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,SAAS,CAAC,CAAA;AAEvD,MAAM,WAAW,GAAG,CAAC,CAAU,EAAE,CAAS,EAAW,EAAE,CACrD,CAAC,KAAK,SAAS,IAAI,OAAO,CAAC,KAAK,CAAC,CAAA;AACnC,MAAM,gBAAgB,GAAG,CAAC,CAAU,EAAE,CAAS,EAAW,EAAE,CAC1D,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;AAEvE,MAAM,aAAa,GAAG,CAAC,CAAU,EAAE,EAAsB,EAAW,EAAE,CACpE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;AAExE,oDAAoD;AACpD,MAAM,SAAS,GAAG,CAChB,CAO4C,EACpC,EAAE,CACV,OAAO,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ;IAChC,CAAC,CAAC,OAAO,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS;QACpC,CAAC,CAAC,OAAO,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ;YAClC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClB,SAAS,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;gBAC1D,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAA;AAExC,MAAM,SAAS,GAAG,CAAC,KAAe,EAAU,EAAE,CAC5C,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC;IAClD,KAAK,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAA;AAE1B,MAAM,YAAY,GAAG,CACnB,CAAU,EACV,IAAO,EACP,KAAQ,EACe,EAAE;IACzB,IAAI,KAAK,EAAE,CAAC;QACV,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;YAAE,OAAO,KAAK,CAAA;QACnC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAU,EAAE,EAAE,CAAC,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,CAAA;IAC/D,CAAC;IACD,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;QAAE,OAAO,KAAK,CAAA;IAClC,OAAO,OAAO,CAAC,KAAK,IAAI,CAAA;AAC1B,CAAC,CAAA;AAED,MAAM,CAAC,MAAM,cAAc,GAAG,CAC5B,CAAM,EACN,IAAO,EACP,KAAQ,EACqB,EAAE,CAC/B,CAAC,CAAC,CAAC;IACH,OAAO,CAAC,KAAK,QAAQ;IACrB,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC;IACpB,CAAC,CAAC,IAAI,KAAK,IAAI;IACf,WAAW,CAAC,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC;IAC9B,WAAW,CAAC,CAAC,CAAC,WAAW,EAAE,QAAQ,CAAC;IACpC,WAAW,CAAC,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC;IAC7B,WAAW,CAAC,CAAC,CAAC,QAAQ,EAAE,UAAU,CAAC;IACnC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC;QACrB,CAAC,CAAC,YAAY,KAAK,SAAS;QAC9B,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC,CAAC,CAAC,OAAO,KAAK,SAAS,IAAI,YAAY,CAAC,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;IACjE,CAAC,CAAC,CAAC,CAAC,QAAQ,KAAK,KAAK,CAAA;AAuCxB,SAAS,GAAG,CACV,IAAuC,EAAE;IAEzC,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,EAAE,YAAY,EAAE,GAAG,IAAI,EAAE,GAAG,CAAC,CAAA;IAChE,IAAI,GAAG,KAAK,SAAS,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,QAAQ,EAAE,KAAK,CAAC,EAAE,CAAC;QAC7D,MAAM,IAAI,SAAS,CAAC,uBAAuB,EAAE;YAC3C,KAAK,EAAE;gBACL,KAAK,EAAE,GAAG;gBACV,MAAM,EAAE,QAAQ;aACjB;SACF,CAAC,CAAA;IACJ,CAAC;IACD,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,QAAQ,CAAC,EAAE,CAAC;QAC9C,MAAM,IAAI,SAAS,CAAC,sBAAsB,EAAE;YAC1C,KAAK,EAAE;gBACL,KAAK,EAAE,YAAY;gBACnB,MAAM,EAAE,UAAU;aACnB;SACF,CAAC,CAAA;IACJ,CAAC;IACD,MAAM,QAAQ,GACZ,GAAG,CAAC,CAAC;QACF,GAAwD;QAC3D,CAAC,CAAC,SAAS,CAAA;IACb,OAAO;QACL,GAAG,IAAI;QACP,OAAO,EAAE,GAAG;QACZ,QAAQ;QACR,YAAY;QACZ,IAAI,EAAE,QAAQ;QACd,QAAQ,EAAE,KAAK;KAChB,CAAA;AACH,CAAC;AAED,SAAS,OAAO,CACd,IAAgC,EAAE;IAElC,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,EAAE,YAAY,EAAE,GAAG,IAAI,EAAE,GAAG,CAAC,CAAA;IAChE,IAAI,GAAG,KAAK,SAAS,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC;QAC5D,MAAM,IAAI,SAAS,CAAC,uBAAuB,EAAE;YAC3C,KAAK,EAAE;gBACL,KAAK,EAAE,GAAG;gBACV,MAAM,EAAE,UAAU;aACnB;SACF,CAAC,CAAA;IACJ,CAAC;IACD,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,QAAQ,CAAC,EAAE,CAAC;QAC9C,MAAM,IAAI,SAAS,CAAC,sBAAsB,EAAE;YAC1C,KAAK,EAAE;gBACL,KAAK,EAAE,YAAY;gBACnB,MAAM,EAAE,UAAU;aACnB;SACF,CAAC,CAAA;IACJ,CAAC;IACD,MAAM,QAAQ,GACZ,GAAG,CAAC,CAAC;QACF,GAAuD;QAC1D,CAAC,CAAC,SAAS,CAAA;IACb,OAAO;QACL,GAAG,IAAI;QACP,OAAO,EAAE,GAAG;QACZ,QAAQ;QACR,YAAY;QACZ,IAAI,EAAE,QAAQ;QACd,QAAQ,EAAE,IAAI;KACf,CAAA;AACH,CAAC;AAED,SAAS,GAAG,CACV,IAAuC,EAAE;IAEzC,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,EAAE,YAAY,EAAE,GAAG,IAAI,EAAE,GAAG,CAAC,CAAA;IAChE,IAAI,GAAG,KAAK,SAAS,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,QAAQ,EAAE,KAAK,CAAC,EAAE,CAAC;QAC7D,MAAM,IAAI,SAAS,CAAC,uBAAuB,EAAE;YAC3C,KAAK,EAAE;gBACL,KAAK,EAAE,GAAG;gBACV,MAAM,EAAE,QAAQ;aACjB;SACF,CAAC,CAAA;IACJ,CAAC;IACD,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,QAAQ,CAAC,EAAE,CAAC;QAC9C,MAAM,IAAI,SAAS,CAAC,sBAAsB,EAAE;YAC1C,KAAK,EAAE;gBACL,KAAK,EAAE,YAAY;gBACnB,MAAM,EAAE,UAAU;aACnB;SACF,CAAC,CAAA;IACJ,CAAC;IACD,MAAM,QAAQ,GACZ,GAAG,CAAC,CAAC;QACF,GAAwD;QAC3D,CAAC,CAAC,SAAS,CAAA;IACb,OAAO;QACL,GAAG,IAAI;QACP,OAAO,EAAE,GAAG;QACZ,QAAQ;QACR,YAAY;QACZ,IAAI,EAAE,QAAQ;QACd,QAAQ,EAAE,KAAK;KAChB,CAAA;AACH,CAAC;AAED,SAAS,OAAO,CACd,IAAgC,EAAE;IAElC,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,EAAE,YAAY,EAAE,GAAG,IAAI,EAAE,GAAG,CAAC,CAAA;IAChE,IAAI,GAAG,KAAK,SAAS,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC;QAC5D,MAAM,IAAI,SAAS,CAAC,uBAAuB,EAAE;YAC3C,KAAK,EAAE;gBACL,KAAK,EAAE,GAAG;gBACV,MAAM,EAAE,UAAU;aACnB;SACF,CAAC,CAAA;IACJ,CAAC;IACD,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,QAAQ,CAAC,EAAE,CAAC;QAC9C,MAAM,IAAI,SAAS,CAAC,sBAAsB,EAAE;YAC1C,KAAK,EAAE;gBACL,KAAK,EAAE,YAAY;gBACnB,MAAM,EAAE,UAAU;aACnB;SACF,CAAC,CAAA;IACJ,CAAC;IACD,MAAM,QAAQ,GACZ,GAAG,CAAC,CAAC;QACF,GAAuD;QAC1D,CAAC,CAAC,SAAS,CAAA;IACb,OAAO;QACL,GAAG,IAAI;QACP,OAAO,EAAE,GAAG;QACZ,QAAQ;QACR,YAAY;QACZ,IAAI,EAAE,QAAQ;QACd,QAAQ,EAAE,IAAI;KACf,CAAA;AACH,CAAC;AAED,SAAS,IAAI,CACX,IAAwC,EAAE;IAE1C,MAAM,EACJ,IAAI,EACJ,OAAO,EAAE,GAAG,EACZ,QAAQ,EAAE,GAAG,EACb,GAAG,IAAI,EACR,GAAG,CAAuC,CAAA;IAC3C,OAAQ,IAA0C,CAAC,YAAY,CAAA;IAC/D,IAAI,GAAG,KAAK,SAAS,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,SAAS,EAAE,KAAK,CAAC,EAAE,CAAC;QAC9D,MAAM,IAAI,SAAS,CAAC,uBAAuB,CAAC,CAAA;IAC9C,CAAC;IACD,MAAM,QAAQ,GACZ,GAAG,CAAC,CAAC;QACF,GAAyD;QAC5D,CAAC,CAAC,SAAS,CAAA;IACb,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;QACvB,MAAM,IAAI,SAAS,CAAC,8BAA8B,CAAC,CAAA;IACrD,CAAC;IACD,OAAO;QACL,GAAG,IAAI;QACP,OAAO,EAAE,GAAG;QACZ,QAAQ;QACR,IAAI,EAAE,SAAS;QACf,QAAQ,EAAE,KAAK;KAChB,CAAA;AACH,CAAC;AAED,SAAS,QAAQ,CACf,IAAiC,EAAE;IAEnC,MAAM,EACJ,IAAI,EACJ,OAAO,EAAE,GAAG,EACZ,QAAQ,EAAE,GAAG,EACb,GAAG,IAAI,EACR,GAAG,CAAuC,CAAA;IAC3C,OAAQ,IAA0C,CAAC,YAAY,CAAA;IAC/D,IAAI,GAAG,KAAK,SAAS,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,CAAC,EAAE,CAAC;QAC7D,MAAM,IAAI,SAAS,CAAC,uBAAuB,CAAC,CAAA;IAC9C,CAAC;IACD,MAAM,QAAQ,GACZ,GAAG,CAAC,CAAC;QACF,GAAwD;QAC3D,CAAC,CAAC,SAAS,CAAA;IACb,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;QACvB,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC,CAAA;IAC1D,CAAC;IACD,OAAO;QACL,GAAG,IAAI;QACP,OAAO,EAAE,GAAG;QACZ,QAAQ;QACR,IAAI,EAAE,SAAS;QACf,QAAQ,EAAE,IAAI;KACf,CAAA;AACH,CAAC;AACD,MAAM,wBAAwB,GAAG,CAC/B,OAAkB,EAC8B,EAAE;IAClD,MAAM,CAAC,GAAmD,EAAE,CAAA;IAC5D,KAAK,MAAM,UAAU,IAAI,OAAO,EAAE,CAAC;QACjC,MAAM,MAAM,GAAG,OAAO,CAAC,UAAU,CAAC,CAAA;QAClC,qBAAqB;QACrB,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,4BAA4B,GAAG,UAAU,CAAC,CAAA;QAC5D,CAAC;QACD,qBAAqB;QACrB,IAAI,cAAc,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC;YAC3C,CAAC,CAAC,UAAU,CAAC,GAAG;gBACd,IAAI,EAAE,QAAQ;gBACd,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,MAAM,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;aAC7C,CAAA;QACH,CAAC;aAAM,IAAI,cAAc,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC,EAAE,CAAC;YACnD,CAAC,CAAC,UAAU,CAAC,GAAG;gBACd,IAAI,EAAE,QAAQ;gBACd,QAAQ,EAAE,KAAK;gBACf,OAAO,EACL,MAAM,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC;oBAC5B,SAAS;oBACX,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC;aAC3B,CAAA;QACH,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,GAAG,MAEkB,CAAA;YAC/B,CAAC,CAAC,UAAU,CAAC,GAAG;gBACd,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ;gBACzB,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB,CAAA;QACH,CAAC;QACD,MAAM,GAAG,GAAG,CAAC,CAAC,UAAU,CAAiC,CAAA;QACzD,IAAI,OAAO,MAAM,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;YACrC,GAAG,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAA;QAC1B,CAAC;QAED,IACE,MAAM,CAAC,IAAI,KAAK,SAAS;YACzB,CAAC,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC;YAC7B,CAAC,OAAO,CAAC,MAAM,UAAU,EAAE,CAAC,EAC5B,CAAC;YACD,CAAC,CAAC,MAAM,UAAU,EAAE,CAAC,GAAG;gBACtB,IAAI,EAAE,SAAS;gBACf,QAAQ,EAAE,MAAM,CAAC,QAAQ;aAC1B,CAAA;QACH,CAAC;IACH,CAAC;IACD,OAAO,CAAC,CAAA;AACV,CAAC,CAAA;AA6BD,MAAM,SAAS,GAAG,CAAC,CAAoB,EAAgB,EAAE,CACvD,CAAC,CAAC,IAAI,KAAK,SAAS,CAAA;AAgBtB,MAAM,aAAa,GAAG,CAAC,CAAoB,EAAoB,EAAE,CAC/D,CAAC,CAAC,IAAI,KAAK,aAAa,CAAA;AAwE1B;;;GAGG;AACH,MAAM,OAAO,IAAI;IACf,UAAU,CAAG;IACb,OAAO,CAAyB;IAChC,QAAQ,CAAa;IACrB,OAAO,GAAiB,EAAE,CAAA;IAC1B,IAAI,CAAqC;IACzC,UAAU,CAAS;IACnB,iBAAiB,CAAS;IAC1B,MAAM,CAAS;IACf,cAAc,CAAS;IAEvB,YAAY,UAAuB,EAAE;QACnC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAA;QACvB,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAA;QAC3D,IAAI,CAAC,IAAI;YACP,IAAI,CAAC,QAAQ,CAAC,GAAG,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAA;QACnE,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,SAAS,CAAA;QACnC,uEAAuE;QACvE,wEAAwE;QACxE,uDAAuD;QACvD,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAM,CAAA;QAC1C,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IACpC,CAAC;IAED;;;;;OAKG;IACH,eAAe,CAAC,MAAyB,EAAE,MAAM,GAAG,EAAE;QACpD,IAAI,CAAC;YACH,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;QACvB,CAAC;QAAC,OAAO,EAAE,EAAE,CAAC;YACZ,MAAM,CAAC,GAAG,EAAW,CAAA;YACrB,IAAI,MAAM,IAAI,CAAC,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE,CAAC;gBACzC,IAAI,CAAC,CAAC,KAAK,IAAI,OAAO,CAAC,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;oBAC3C,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAA;gBAC1C,CAAC;qBAAM,CAAC;oBACN,CAAC,CAAC,KAAK,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,CAAA;gBAC5B,CAAC;YACH,CAAC;YACD,MAAM,CAAC,CAAA;QACT,CAAC;QACD,KAAK,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YACpD,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;YACjC,2CAA2C;YAC3C,qBAAqB;YACrB,IAAI,CAAC,EAAE,EAAE,CAAC;gBACR,MAAM,IAAI,KAAK,CAAC,kCAAkC,GAAG,KAAK,EAAE;oBAC1D,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE;iBACxB,CAAC,CAAA;YACJ,CAAC;YACD,oBAAoB;YACpB,EAAE,CAAC,OAAO,GAAG,KAAK,CAAA;QACpB,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;;;;;;OAUG;IACH,KAAK,CAAC,OAAiB,OAAO,CAAC,IAAI;QACjC,IAAI,CAAC,eAAe,EAAE,CAAA;QACtB,MAAM,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;QAC7B,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAA;QACrB,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;QAChB,OAAO,CAAC,CAAA;IACV,CAAC;IAED,eAAe;QACb,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,KAAK,MAAM,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC1D,MAAM,EAAE,GAAG,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAA;gBAC3C,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;gBACzB,IAAI,GAAG,KAAK,SAAS,EAAE,CAAC;oBACtB,EAAE,CAAC,OAAO,GAAG,UAAU,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,KAAK,CAAC,CAAA;gBAChE,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED,aAAa,CAAC,CAAY;QACxB,KAAK,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YACzD,IAAI,CAAC,CAAC,OAAO,KAAK,SAAS,IAAI,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC;gBACpD,YAAY;gBACZ,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,CAAA;YAC7B,CAAC;QACH,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACH,QAAQ,CAAC,IAAc;QACrB,IAAI,IAAI,KAAK,OAAO,CAAC,IAAI,EAAE,CAAC;YAC1B,IAAI,GAAG,IAAI,CAAC,KAAK,CACd,OAA8B,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAC5D,CAAA;QACH,CAAC;QAED,MAAM,OAAO,GAAG,wBAAwB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;QACzD,MAAM,MAAM,GAAG,SAAS,CAAC;YACvB,IAAI;YACJ,OAAO;YACP,yCAAyC;YACzC,MAAM,EAAE,KAAK;YACb,gBAAgB,EAAE,IAAI,CAAC,iBAAiB;YACxC,MAAM,EAAE,IAAI;SACb,CAAC,CAAA;QAEF,MAAM,CAAC,GAAc;YACnB,MAAM,EAAE,EAAE;YACV,WAAW,EAAE,EAAE;SAChB,CAAA;QACD,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAClC,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;gBAChC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;gBAC/B,IACE,IAAI,CAAC,QAAQ,CAAC,gBAAgB;oBAC9B,IAAI,CAAC,QAAQ,CAAC,oBAAoB,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EACjD,CAAC;oBACD,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAA;oBAClD,MAAK;gBACP,CAAC;YACH,CAAC;iBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBACnC,IAAI,KAAK,GAA0C,SAAS,CAAA;gBAC5D,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;oBACjC,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;oBACtC,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;oBAChD,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;oBAClC,IACE,GAAG;wBACH,GAAG,CAAC,IAAI,KAAK,SAAS;wBACtB,CAAC,CAAC,EAAE;4BACF,CAAC,EAAE,CAAC,IAAI,KAAK,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,QAAQ,KAAK,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAC9D,CAAC;wBACD,KAAK,GAAG,KAAK,CAAA;wBACb,KAAK,CAAC,IAAI,GAAG,KAAK,CAAA;oBACpB,CAAC;gBACH,CAAC;gBACD,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;gBACtC,IAAI,CAAC,EAAE,EAAE,CAAC;oBACR,MAAM,IAAI,KAAK,CACb,mBAAmB,KAAK,CAAC,OAAO,KAAK;wBACnC,wDAAwD;wBACxD,uDAAuD;wBACvD,OAAO,KAAK,CAAC,OAAO,GAAG,EACzB;wBACE,KAAK,EAAE;4BACL,KAAK,EACH,KAAK,CAAC,OAAO,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;yBACzD;qBACF,CACF,CAAA;gBACH,CAAC;gBACD,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;oBACxB,IAAI,KAAK,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;wBAC9B,IAAI,EAAE,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;4BAC1B,MAAM,IAAI,KAAK,CACb,yBAAyB,KAAK,CAAC,OAAO,cAAc,EAAE,CAAC,IAAI,EAAE,EAC7D;gCACE,KAAK,EAAE;oCACL,IAAI,EAAE,KAAK,CAAC,OAAO;oCACnB,MAAM,EAAE,SAAS,CAAC,EAAE,CAAC;iCACtB;6BACF,CACF,CAAA;wBACH,CAAC;wBACD,KAAK,GAAG,IAAI,CAAA;oBACd,CAAC;yBAAM,CAAC;wBACN,IAAI,EAAE,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;4BAC1B,MAAM,IAAI,KAAK,CACb,QAAQ,KAAK,CAAC,OAAO,qCAAqC,KAAK,CAAC,KAAK,GAAG,EACxE,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,CAC5B,CAAA;wBACH,CAAC;wBACD,IAAI,EAAE,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;4BACzB,KAAK,GAAG,KAAK,CAAC,KAAK,CAAA;wBACrB,CAAC;6BAAM,CAAC;4BACN,KAAK,GAAG,CAAC,KAAK,CAAC,KAAK,CAAA;4BACpB,IAAI,KAAK,KAAK,KAAK,EAAE,CAAC;gCACpB,MAAM,IAAI,KAAK,CACb,kBAAkB,KAAK,CAAC,KAAK,iBAAiB;oCAC5C,IAAI,KAAK,CAAC,OAAO,2BAA2B,EAC9C;oCACE,KAAK,EAAE;wCACL,IAAI,EAAE,KAAK,CAAC,OAAO;wCACnB,KAAK,EAAE,KAAK,CAAC,KAAK;wCAClB,MAAM,EAAE,QAAQ;qCACjB;iCACF,CACF,CAAA;4BACH,CAAC;wBACH,CAAC;oBACH,CAAC;gBACH,CAAC;gBACD,IAAI,EAAE,CAAC,QAAQ,EAAE,CAAC;oBAChB,MAAM,EAAE,GAAG,CAAC,CAAC,MAEZ,CAAA;oBACD,MAAM,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAA;oBAC/B,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,CAAA;oBACnB,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;gBAChB,CAAC;qBAAM,CAAC;oBACN,MAAM,EAAE,GAAG,CAAC,CAAC,MAAoD,CAAA;oBACjE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAA;gBACxB,CAAC;YACH,CAAC;QACH,CAAC;QAED,KAAK,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC;YACtD,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,QAAQ,CAAA;YAC9C,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,YAAY,CAAA;YACzD,IAAI,KAMC,CAAA;YACL,IAAI,YAAY,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,YAAY,CAAC,EAAE,CAAC;gBACxD,KAAK,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,YAAY,EAAE,CAAA;YACnE,CAAC;YACD,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC3B,KAAK,GAAG,KAAK,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAA;YAChD,CAAC;YACD,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,IAAI,KAAK,CACb,gCAAgC,KAAK,KAAK,IAAI,CAAC,SAAS,CACtD,KAAK,CACN,EAAE,EACH,EAAE,KAAK,EAAE,CACV,CAAA;YACH,CAAC;QACH,CAAC;QAED,OAAO,CAAC,CAAA;IACV,CAAC;IAED;;;OAGG;IACH,WAAW,CAAC,CAAS,EAAE,GAAY,EAAE,IAAY,CAAC;QAChD,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,OAAO,GAAG,KAAK,SAAS;YAAE,OAAM;QAC5D,MAAM,GAAG,GAAG,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;QACrC,uDAAuD;QACvD,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAA;QAC7B,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,IAAI,KAAK,SAAS,EAAE,CAAC;YAC7C,MAAM,IAAI,KAAK,CACb,eAAe,CAAC,mBAAmB,GAAG,eAAe,EACrD,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,CACrC,CAAA;QACH,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,QAAQ,CAAC,CAAU;QACjB,IAAI,CAAC,CAAC,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,CAAC,+BAA+B,EAAE;gBAC/C,KAAK,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE;aACpB,CAAC,CAAA;QACJ,CAAC;QACD,MAAM,IAAI,GAAG,CAA+B,CAAA;QAC5C,KAAK,MAAM,KAAK,IAAI,CAAC,EAAE,CAAC;YACtB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAA;YACzB,6BAA6B;YAC7B,IAAI,KAAK,KAAK,SAAS;gBAAE,SAAQ;YACjC,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;YAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;YACrC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,0BAA0B,KAAK,EAAE,EAAE;oBACjD,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE;iBACxB,CAAC,CAAA;YACJ,CAAC;YACD,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACzD,MAAM,IAAI,KAAK,CACb,iBAAiB,SAAS,CACxB,KAAK,CACN,QAAQ,KAAK,cAAc,SAAS,CAAC,MAAM,CAAC,EAAE,EAC/C;oBACE,KAAK,EAAE;wBACL,IAAI,EAAE,KAAK;wBACX,KAAK,EAAE,KAAK;wBACZ,MAAM,EAAE,SAAS,CAAC,MAAM,CAAC;qBAC1B;iBACF,CACF,CAAA;YACH,CAAC;YACD,IAAI,KAMC,CAAA;YACL,IACE,MAAM,CAAC,YAAY;gBACnB,CAAC,aAAa,CAAC,KAAK,EAAE,MAAM,CAAC,YAAY,CAAC,EAC1C,CAAC;gBACD,KAAK,GAAG;oBACN,IAAI,EAAE,KAAK;oBACX,KAAK,EAAE,KAAK;oBACZ,YAAY,EAAE,MAAM,CAAC,YAAY;iBAClC,CAAA;YACH,CAAC;YACD,IAAI,MAAM,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC/C,KAAK,GAAG,KAAK,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAA;YAChD,CAAC;YACD,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,IAAI,KAAK,CAAC,4BAA4B,KAAK,KAAK,KAAK,EAAE,EAAE;oBAC7D,KAAK;iBACN,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAED,QAAQ,CAAC,CAAY;QACnB,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU;YAAE,OAAM;QAC1C,KAAK,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC;YACtD,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;YACjC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC,GAAG,QAAQ,CACpD,KAAK,EACL,EAAE,EAAE,KAAK,CACV,CAAA;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,OAAO,CACL,IAAY,EACZ,KAA6B,EAC7B,EAAE,GAAG,GAAG,KAAK,KAAwB,EAAE;QAEvC,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;YACxB,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QACtD,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAA;QACxD,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,IAAY,EAAE,EAAE,GAAG,KAAwB,EAAE;QACvD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAA;QACrD,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;OAEG;IACH,GAAG,CACD,MAAS;QAET,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;IACrC,CAAC;IAED;;OAEG;IACH,OAAO,CACL,MAAS;QAET,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;IACzC,CAAC;IAED;;OAEG;IACH,GAAG,CACD,MAAS;QAET,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;IACrC,CAAC;IAED;;OAEG;IACH,OAAO,CACL,MAAS;QAET,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;IACzC,CAAC;IAED;;OAEG;IACH,IAAI,CACF,MAAS;QAET,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;IACtC,CAAC;IAED;;OAEG;IACH,QAAQ,CACN,MAAS;QAET,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;IAC1C,CAAC;IAED;;;;OAIG;IACH,SAAS,CAAsB,MAAS;QACtC,MAAM,IAAI,GAAG,IAA8B,CAAA;QAC3C,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YACnD,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;YAC/B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;gBAChB,IAAI,EAAE,QAAQ;gBACd,IAAI;gBACJ,KAAK,EAAE,KAAqC;aAC7C,CAAC,CAAA;QACJ,CAAC;QACD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC,CAAA;QACtC,OAAO,IAAI,CAAA;IACb,CAAC;IAED,UAAU,CAKR,MAAS,EACT,EAAyD;QAGzD,MAAM,IAAI,GAAG,IAA8B,CAAA;QAC3C,MAAM,CAAC,MAAM,CACX,IAAI,CAAC,UAAU,EACf,MAAM,CAAC,WAAW,CAChB,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE;YAC3C,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;YAC/B,MAAM,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,CAAA;YACxB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;gBAChB,IAAI,EAAE,QAAQ;gBACd,IAAI;gBACJ,KAAK,EAAE,MAAsC;aAC9C,CAAC,CAAA;YACF,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;QACvB,CAAC,CAAC,CACH,CACF,CAAA;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,aAAa,CAAC,IAAY,EAAE,KAAyB;QACnD,IAAI,CAAC,0CAA0C,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3D,MAAM,IAAI,SAAS,CACjB,wBAAwB,IAAI,IAAI;gBAC9B,0CAA0C,CAC7C,CAAA;QACH,CAAC;QACD,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1B,MAAM,IAAI,SAAS,CAAC,0BAA0B,KAAK,EAAE,CAAC,CAAA;QACxD,CAAC;QACD,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACvB,MAAM,IAAI,SAAS,CACjB,0BAA0B,IAAI,YAAY;gBACxC,cAAc,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CACrC,CAAA;QACH,CAAC;QACD,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;YAChB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;gBACvC,MAAM,IAAI,SAAS,CACjB,WAAW,IAAI,kBAAkB,KAAK,CAAC,KAAK,IAAI;oBAC9C,wCAAwC,CAC3C,CAAA;YACH,CAAC;YACD,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC9B,MAAM,IAAI,SAAS,CACjB,WAAW,IAAI,kBAAkB,KAAK,CAAC,KAAK,IAAI;oBAC9C,sBAAsB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CACpD,CAAA;YACH,CAAC;YACD,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,IAAI,CAAA;YAChC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAA;QAC3B,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,IAAI,CAAC,MAAM;YAAE,OAAO,IAAI,CAAC,MAAM,CAAA;QAEnC,IAAI,YAAY,GAAG,CAAC,CAAA;QACpB,MAAM,EAAE,GAAG,KAAK,CAAC,EAAE,KAAK,EAAE,CAAC,CAAA;QAC3B,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;QAC7B,IAAI,KAAK,GAAG,KAAK,EAAE,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QAC7C,IAAI,KAAK,EAAE,IAAI,KAAK,SAAS,EAAE,CAAC;YAC9B,EAAE,CAAC,GAAG,CAAC;gBACL,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBACrB,IAAI,EAAE,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;aAC5B,CAAC,CAAA;QACJ,CAAC;QACD,EAAE,CAAC,GAAG,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAA;QACjD,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;YACxB,EAAE,CAAC,GAAG,CAAC;gBACL,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK;gBACzB,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;aACtB,CAAC,CAAA;QACJ,CAAC;aAAM,CAAC;YACN,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YAC7C,MAAM,UAAU,GAAa,EAAE,CAAA;YAC/B,MAAM,MAAM,GAAe,EAAE,CAAA;YAC7B,MAAM,KAAK,GAAa,EAAE,CAAA;YAC1B,MAAM,IAAI,GAAe,EAAE,CAAA;YAC3B,KAAK,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC9D,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;oBACjB,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS;wBAAE,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;;wBACvD,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC,CAAA;gBACxD,CAAC;qBAAM,CAAC;oBACN,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS;wBAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;;wBAC3C,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC,CAAA;gBAC/C,CAAC;YACH,CAAC;YACD,MAAM,EAAE,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;YAC9D,MAAM,EAAE,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;YAC5D,MAAM,EAAE,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;YAC7C,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;YAC1D,MAAM,KAAK,GAAG,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,IAAI,EAAE,CAAA;YACjD,EAAE,CAAC,GAAG,CAAC;gBACL,IAAI,EAAE,KAAK;gBACX,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;aACtB,CAAC,CAAA;QACJ,CAAC;QAED,EAAE,CAAC,GAAG,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAA;QAC3C,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;QACrC,IAAI,SAAS,IAAI,aAAa,CAAC,SAAS,CAAC,EAAE,CAAC;YAC1C,MAAM,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,GAAG,CAAC,CAAA;YACtD,KAAK,EAAE,CAAA;YACP,EAAE,CAAC,GAAG,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAA;YAC9C,EAAE,CAAC,GAAG,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAA;QAC7C,CAAC;QAED,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;QAEjD,+DAA+D;QAC/D,gBAAgB;QAChB,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;gBACb,wCAAwC;gBACxC,oDAAoD;gBACpD,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,CAAA;gBACtD,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,QAAQ,GAAG,CAAC,EAAE,CAAC;oBACnC,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC,CAAA;oBAC5D,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAA;gBAC1D,CAAC;qBAAM,CAAC;oBACN,EAAE,CAAC,GAAG,CACJ;wBACE,IAAI,EAAE,GAAG,CAAC,IAAI;wBACd,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,YAAY,CAAC;wBAChC,KAAK,EAAE,QAAQ;qBAChB,EACD,EAAE,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,CAC1C,CAAA;gBACH,CAAC;gBACD,IAAI,GAAG,CAAC,QAAQ,EAAE,CAAC;oBACjB,EAAE,CAAC,GAAG,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAA;gBAC7C,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,IAAI,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC;oBACnB,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,CAAA;oBACrB,YAAY,GAAG,KAAK,CAAA;oBACpB,qCAAqC;oBACrC,eAAe;oBACf,MAAM,CAAC,GAAG,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;oBAC5B,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,EAAE,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAA;gBACvD,CAAC;qBAAM,CAAC;oBACN,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,EAAE,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;gBAClE,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAA;IACtC,CAAC;IAED;;OAEG;IACH,aAAa;QACX,IAAI,IAAI,CAAC,cAAc;YAAE,OAAO,IAAI,CAAC,cAAc,CAAA;QAEnD,MAAM,GAAG,GAAa,EAAE,CAAA;QAExB,IAAI,YAAY,GAAG,CAAC,CAAA;QACpB,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;QAC7B,IAAI,KAAK,GAAG,KAAK,EAAE,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QAC7C,IAAI,KAAK,EAAE,IAAI,KAAK,SAAS,EAAE,CAAC;YAC9B,GAAG,CAAC,IAAI,CAAC,KAAK,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;QAC/C,CAAC;QACD,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QAClB,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;YACxB,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAA;QACxD,CAAC;aAAM,CAAC;YACN,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YAC7C,MAAM,UAAU,GAAa,EAAE,CAAA;YAC/B,MAAM,MAAM,GAAe,EAAE,CAAA;YAC7B,MAAM,KAAK,GAAa,EAAE,CAAA;YAC1B,MAAM,IAAI,GAAe,EAAE,CAAA;YAC3B,KAAK,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC9D,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;oBACjB,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS;wBAAE,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;;wBACvD,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC,CAAA;gBACxD,CAAC;qBAAM,CAAC;oBACN,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS;wBAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;;wBAC3C,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC,CAAA;gBAC/C,CAAC;YACH,CAAC;YACD,MAAM,EAAE,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;YAC9D,MAAM,EAAE,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;YAC5D,MAAM,EAAE,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;YAC7C,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;YAC1D,MAAM,KAAK,GAAG,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,IAAI,EAAE,CAAA;YACjD,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAA;QAC1C,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;QACrC,IAAI,SAAS,IAAI,aAAa,CAAC,SAAS,CAAC,EAAE,CAAC;YAC1C,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,CAAA;YAC1D,KAAK,EAAE,CAAA;QACT,CAAC;QAED,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;QAEvC,yDAAyD;QACzD,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;gBACb,GAAG,CAAC,IAAI,CACN,GAAG,CAAC,MAAM,CAAC,YAAY,GAAG,CAAC,CAAC;oBAC1B,GAAG;oBACH,gBAAgB,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CACnC,CAAA;gBACD,IAAI,GAAG,CAAC,IAAI;oBAAE,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAA;YACrD,CAAC;iBAAM,IAAI,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC1B,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,CAAA;gBACrB,YAAY,GAAG,KAAK,CAAA;gBACpB,GAAG,CAAC,IAAI,CACN,GAAG,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,gBAAgB,CAC7C,GAAG,CAAC,IAAI,EACR,GAAG,CAAC,GAAG,CACR,EAAE,CACJ,CAAA;YACH,CAAC;iBAAM,CAAC;gBACN,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAE,GAAmB,CAAC,GAAG,CAAC,CAAC,CAAA;YACnE,CAAC;QACH,CAAC;QAED,OAAO,CAAC,IAAI,CAAC,cAAc,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,CAAA;IACxD,CAAC;IAED,UAAU,CAAC,KAAa;QACtB,oEAAoE;QACpE,qDAAqD;QACrD,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;QAC9D,IAAI,QAAQ,GAAG,CAAC,CAAA;QAChB,IAAI,IAAI,GAA8B,SAAS,CAAA;QAC/C,MAAM,IAAI,GAAsB,EAAE,CAAA;QAClC,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;YAC9C,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC5B,IAAI,IAAI,EAAE,IAAI,KAAK,QAAQ;oBAAE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;gBACjD,IAAI,GAAG,SAAS,CAAA;gBAChB,KAAK,CAAC,IAAI,GAAG,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;gBAC/C,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;gBAChB,SAAQ;YACV,CAAC;YACD,MAAM,EAAE,KAAK,EAAE,GAAG,KAAK,CAAA;YACvB,MAAM,IAAI,GAAG,KAAK,CAAC,WAAW,IAAI,EAAE,CAAA;YACpC,MAAM,IAAI,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,2BAA2B,CAAC,CAAC,CAAC,EAAE,CAAA;YAC9D,MAAM,IAAI,GACR,KAAK,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;gBAC1B,iBAAiB,KAAK,CAAC,YAAY,CAAC,GAAG,CACrC,CAAC,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAC7B,EAAE;gBACL,CAAC,CAAC,EAAE,CAAA;YACN,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAA;YACnD,MAAM,KAAK,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,CAAA;YAC/C,MAAM,IAAI,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,OAAO,GAAG,KAAK,CAAC,CAAC,IAAI,EAAE,CAAA;YACvD,MAAM,IAAI,GACR,KAAK,CAAC,IAAI;gBACV,CAAC,KAAK,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG;oBAC9B,CAAC,CAAC,KAAK,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI;wBACtC,CAAC,CAAC,SAAS,CAAC,CAAA;YACd,MAAM,KAAK,GACT,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;gBACjB,CAAC,CAAC,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,KAAK,GAAG;oBAC/C,CAAC,CAAC,IAAI,KAAK,CAAC,KAAK,IAAI,IAAI,IAAI,CAAA;YAC/B,MAAM,IAAI,GACR,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC;gBACxB,GAAG,KAAK,KAAK,KAAK,CAAC,IAAI,EAAE;gBAC3B,CAAC,CAAC,GAAG,KAAK,KAAK,KAAK,CAAC,IAAI,KAAK,IAAI,GAAG,CAAA;YACvC,MAAM,GAAG,GAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAA;YAC/C,IAAI,IAAI,CAAC,MAAM,GAAG,KAAK,GAAG,MAAM,EAAE,CAAC;gBACjC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAA;YACrB,CAAC;YACD,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM;gBAAE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;YACtD,IAAI,GAAG,GAAG,CAAA;YACV,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA;YAC3B,IAAI,GAAG,GAAG,QAAQ,IAAI,GAAG,GAAG,MAAM,EAAE,CAAC;gBACnC,QAAQ,GAAG,GAAG,CAAA;YAChB,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAChB,CAAC;QAED,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAA;IAC3B,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,OAAO,MAAM,CAAC,WAAW,CACvB,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC;YACpD,KAAK;YACL;gBACE,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC3C,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC1C,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC1C,GAAG,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;oBACnB,EAAE,WAAW,EAAE,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE;oBAC7C,CAAC,CAAC,EAAE,CAAC;gBACL,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBACnD,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,GAAG,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC/D,GAAG,CAAC,GAAG,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC9D,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;aACxC;SACF,CAAC,CACH,CAAA;IACH,CAAC;IAED;;OAEG;IACH,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAS,EAAE,OAAuB;QACjD,OAAO,QAAQ,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,OAAO,CAAC,EAAE,CAAA;IAClD,CAAC;CACF;AAED,mDAAmD;AACnD,oDAAoD;AACpD,MAAM,SAAS,GAAG,CAAC,CAAS,EAAE,GAAG,GAAG,KAAK,EAAE,EAAE;IAC3C,IAAI,GAAG;QACL,yDAAyD;QACzD,OAAO,CAAC;aACL,KAAK,CAAC,IAAI,CAAC;aACX,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC;aACtB,IAAI,CAAC,IAAI,CAAC,CAAA;IACf,OAAO,CAAC;SACL,KAAK,CAAC,eAAe,CAAC;SACtB,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QACZ,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YAChB,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC;gBACd,OAAO,kBAAkB,CAAA;YAC3B,CAAC;YACD,6DAA6D;YAC7D,MAAM,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;YAC3B,wCAAwC;YACxC,KAAK,CAAC,GAAG,EAAE,CAAA;YACX,KAAK,CAAC,KAAK,EAAE,CAAA;YACb,MAAM,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC,EAAE,EAAE;gBACtC,oBAAoB;gBACpB,MAAM,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAA;gBACtC,IAAI,GAAG,CAAC,MAAM;oBAAE,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;;oBAChD,OAAO,QAAQ,CAAA;YACtB,CAAC,EAAE,QAAQ,CAAC,CAAA;YACZ,oBAAoB;YACpB,MAAM,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAA;YAC/B,OAAO,CACL,SAAS;gBACT,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;gBACpD,SAAS,CACV,CAAA;QACH,CAAC;QACD,OAAO,CACL,CAAC;YACC,8CAA8C;aAC7C,OAAO,CAAC,yBAAyB,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAChD,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CACnD;YACD,gCAAgC;aAC/B,OAAO,CAAC,uBAAuB,EAAE,OAAO,CAAC;YAC1C,6BAA6B;aAC5B,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC;YAC3B,2CAA2C;aAC1C,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC;aAC1B,IAAI,EAAE,CACV,CAAA;IACH,CAAC,CAAC;SACD,IAAI,CAAC,IAAI,CAAC,CAAA;AACf,CAAC,CAAA;AAED,kEAAkE;AAClE,MAAM,iBAAiB,GAAG,CAAC,CAAS,EAAE,MAAe,KAAK,EAAU,EAAE;IACpE,MAAM,CAAC,GAAG,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;IAClD,OAAO,GAAG,CAAC,CAAC;QACR,WAAW,CAAC,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,UAAU;QAC/C,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,IAAI,EAAE,CAAA;AACrC,CAAC,CAAA;AAED,MAAM,gBAAgB,GAAG,CAAC,CAAS,EAAE,MAAe,KAAK,EAAE,EAAE;IAC3D,MAAM,CAAC,GAAG,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;SACxB,OAAO,CAAC,cAAc,EAAE,GAAG,CAAC;SAC5B,IAAI,EAAE,CAAA;IACT,OAAO,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;AAC7B,CAAC,CAAA;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,UAAuB,EAAE,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,CAAA", "sourcesContent": ["export type ConfigType = 'number' | 'string' | 'boolean'\n\n/**\n * Given a Jack object, get the typeof its ConfigSet\n */\nexport type Unwrap<J> = J extends Jack<infer C> ? C : never\n\nimport { inspect, InspectOptions, ParseArgsConfig } from 'node:util'\nimport { parseArgs } from './parse-args.js'\n\n// it's a tiny API, just cast it inline, it's fine\n//@ts-ignore\nimport cliui from '@isaacs/cliui'\nimport { basename } from 'node:path'\n\nconst width = Math.min(\n  (process && process.stdout && process.stdout.columns) || 80,\n  80,\n)\n\n// indentation spaces from heading level\nconst indent = (n: number) => (n - 1) * 2\n\nconst toEnvKey = (pref: string, key: string): string => {\n  return [pref, key.replace(/[^a-zA-Z0-9]+/g, ' ')]\n    .join(' ')\n    .trim()\n    .toUpperCase()\n    .replace(/ /g, '_')\n}\n\nconst toEnvVal = (\n  value: string | boolean | number | string[] | boolean[] | number[],\n  delim: string = '\\n',\n): string => {\n  const str =\n    typeof value === 'string' ? value\n    : typeof value === 'boolean' ?\n      value ? '1'\n      : '0'\n    : typeof value === 'number' ? String(value)\n    : Array.isArray(value) ?\n      value.map((v: string | number | boolean) => toEnvVal(v)).join(delim)\n    : /* c8 ignore start */ undefined\n  if (typeof str !== 'string') {\n    throw new Error(\n      `could not serialize value to environment: ${JSON.stringify(value)}`,\n    )\n  }\n  /* c8 ignore stop */\n  return str\n}\n\nconst fromEnvVal = <T extends ConfigType, M extends boolean>(\n  env: string,\n  type: T,\n  multiple: M,\n  delim: string = '\\n',\n): ValidValue<T, M> =>\n  (multiple ?\n    env ? env.split(delim).map(v => fromEnvVal(v, type, false))\n    : []\n  : type === 'string' ? env\n  : type === 'boolean' ? env === '1'\n  : +env.trim()) as ValidValue<T, M>\n\n/**\n * Defines the type of value that is valid, given a config definition's\n * {@link ConfigType} and boolean multiple setting\n */\nexport type ValidValue<\n  T extends ConfigType = ConfigType,\n  M extends boolean = boolean,\n> =\n  [T, M] extends ['number', true] ? number[]\n  : [T, M] extends ['string', true] ? string[]\n  : [T, M] extends ['boolean', true] ? boolean[]\n  : [T, M] extends ['number', false] ? number\n  : [T, M] extends ['string', false] ? string\n  : [T, M] extends ['boolean', false] ? boolean\n  : [T, M] extends ['string', boolean] ? string | string[]\n  : [T, M] extends ['boolean', boolean] ? boolean | boolean[]\n  : [T, M] extends ['number', boolean] ? number | number[]\n  : [T, M] extends [ConfigType, false] ? string | number | boolean\n  : [T, M] extends [ConfigType, true] ? string[] | number[] | boolean[]\n  : string | number | boolean | string[] | number[] | boolean[]\n\n/**\n * The meta information for a config option definition, when the\n * type and multiple values can be inferred by the method being used\n */\nexport type ConfigOptionMeta<\n  T extends ConfigType,\n  M extends boolean = boolean,\n  O extends\n    | undefined\n    | (T extends 'boolean' ? never\n      : T extends 'string' ? readonly string[]\n      : T extends 'number' ? readonly number[]\n      : readonly number[] | readonly string[]) =\n    | undefined\n    | (T extends 'boolean' ? never\n      : T extends 'string' ? readonly string[]\n      : T extends 'number' ? readonly number[]\n      : readonly number[] | readonly string[]),\n> = {\n  default?:\n    | undefined\n    | (ValidValue<T, M> &\n        (O extends number[] | string[] ?\n          M extends false ?\n            O[number]\n          : O[number][]\n        : unknown))\n  validOptions?: O\n  description?: string\n  validate?:\n    | ((v: unknown) => v is ValidValue<T, M>)\n    | ((v: unknown) => boolean)\n  short?: string | undefined\n  type?: T\n  hint?: T extends 'boolean' ? never : string\n  delim?: M extends true ? string : never\n} & (M extends false ? { multiple?: false | undefined }\n: M extends true ? { multiple: true }\n: { multiple?: boolean })\n\n/**\n * A set of {@link ConfigOptionMeta} fields, referenced by their longOption\n * string values.\n */\nexport type ConfigMetaSet<\n  T extends ConfigType,\n  M extends boolean = boolean,\n> = {\n  [longOption: string]: ConfigOptionMeta<T, M>\n}\n\n/**\n * Infer {@link ConfigSet} fields from a given {@link ConfigMetaSet}\n */\nexport type ConfigSetFromMetaSet<\n  T extends ConfigType,\n  M extends boolean,\n  S extends ConfigMetaSet<T, M>,\n> = {\n  [longOption in keyof S]: ConfigOptionBase<T, M>\n}\n\n/**\n * Fields that can be set on a {@link ConfigOptionBase} or\n * {@link ConfigOptionMeta} based on whether or not the field is known to be\n * multiple.\n */\nexport type MultiType<M extends boolean> =\n  M extends true ?\n    {\n      multiple: true\n      delim?: string | undefined\n    }\n  : M extends false ?\n    {\n      multiple?: false | undefined\n      delim?: undefined\n    }\n  : {\n      multiple?: boolean | undefined\n      delim?: string | undefined\n    }\n\n/**\n * A config field definition, in its full representation.\n */\nexport type ConfigOptionBase<\n  T extends ConfigType,\n  M extends boolean = boolean,\n> = {\n  type: T\n  short?: string | undefined\n  default?: ValidValue<T, M> | undefined\n  description?: string\n  hint?: T extends 'boolean' ? undefined : string | undefined\n  validate?: (v: unknown) => v is ValidValue<T, M>\n  validOptions?: T extends 'boolean' ? undefined\n  : T extends 'string' ? readonly string[]\n  : T extends 'number' ? readonly number[]\n  : readonly number[] | readonly string[]\n} & MultiType<M>\n\nexport const isConfigType = (t: string): t is ConfigType =>\n  typeof t === 'string' &&\n  (t === 'string' || t === 'number' || t === 'boolean')\n\nconst undefOrType = (v: unknown, t: string): boolean =>\n  v === undefined || typeof v === t\nconst undefOrTypeArray = (v: unknown, t: string): boolean =>\n  v === undefined || (Array.isArray(v) && v.every(x => typeof x === t))\n\nconst isValidOption = (v: unknown, vo: readonly unknown[]): boolean =>\n  Array.isArray(v) ? v.every(x => isValidOption(x, vo)) : vo.includes(v)\n\n// print the value type, for error message reporting\nconst valueType = (\n  v:\n    | string\n    | number\n    | boolean\n    | string[]\n    | number[]\n    | boolean[]\n    | { type: ConfigType; multiple?: boolean },\n): string =>\n  typeof v === 'string' ? 'string'\n  : typeof v === 'boolean' ? 'boolean'\n  : typeof v === 'number' ? 'number'\n  : Array.isArray(v) ?\n    joinTypes([...new Set(v.map(v => valueType(v)))]) + '[]'\n  : `${v.type}${v.multiple ? '[]' : ''}`\n\nconst joinTypes = (types: string[]): string =>\n  types.length === 1 && typeof types[0] === 'string' ?\n    types[0]\n  : `(${types.join('|')})`\n\nconst isValidValue = <T extends ConfigType, M extends boolean>(\n  v: unknown,\n  type: T,\n  multi: M,\n): v is ValidValue<T, M> => {\n  if (multi) {\n    if (!Array.isArray(v)) return false\n    return !v.some((v: unknown) => !isValidValue(v, type, false))\n  }\n  if (Array.isArray(v)) return false\n  return typeof v === type\n}\n\nexport const isConfigOption = <T extends ConfigType, M extends boolean>(\n  o: any,\n  type: T,\n  multi: M,\n): o is ConfigOptionBase<T, M> =>\n  !!o &&\n  typeof o === 'object' &&\n  isConfigType(o.type) &&\n  o.type === type &&\n  undefOrType(o.short, 'string') &&\n  undefOrType(o.description, 'string') &&\n  undefOrType(o.hint, 'string') &&\n  undefOrType(o.validate, 'function') &&\n  (o.type === 'boolean' ?\n    o.validOptions === undefined\n  : undefOrTypeArray(o.validOptions, o.type)) &&\n  (o.default === undefined || isValidValue(o.default, type, multi)) &&\n  !!o.multiple === multi\n\n/**\n * A set of {@link ConfigOptionBase} objects, referenced by their longOption\n * string values.\n */\nexport type ConfigSet = {\n  [longOption: string]: ConfigOptionBase<ConfigType>\n}\n\n/**\n * The 'values' field returned by {@link Jack#parse}\n */\nexport type OptionsResults<T extends ConfigSet> = {\n  [k in keyof T]?: T[k]['validOptions'] extends (\n    readonly string[] | readonly number[]\n  ) ?\n    T[k] extends ConfigOptionBase<'string' | 'number', false> ?\n      T[k]['validOptions'][number]\n    : T[k] extends ConfigOptionBase<'string' | 'number', true> ?\n      T[k]['validOptions'][number][]\n    : never\n  : T[k] extends ConfigOptionBase<'string', false> ? string\n  : T[k] extends ConfigOptionBase<'string', true> ? string[]\n  : T[k] extends ConfigOptionBase<'number', false> ? number\n  : T[k] extends ConfigOptionBase<'number', true> ? number[]\n  : T[k] extends ConfigOptionBase<'boolean', false> ? boolean\n  : T[k] extends ConfigOptionBase<'boolean', true> ? boolean[]\n  : never\n}\n\n/**\n * The object retured by {@link Jack#parse}\n */\nexport type Parsed<T extends ConfigSet> = {\n  values: OptionsResults<T>\n  positionals: string[]\n}\n\nfunction num(\n  o: ConfigOptionMeta<'number', false> = {},\n): ConfigOptionBase<'number', false> {\n  const { default: def, validate: val, validOptions, ...rest } = o\n  if (def !== undefined && !isValidValue(def, 'number', false)) {\n    throw new TypeError('invalid default value', {\n      cause: {\n        found: def,\n        wanted: 'number',\n      },\n    })\n  }\n  if (!undefOrTypeArray(validOptions, 'number')) {\n    throw new TypeError('invalid validOptions', {\n      cause: {\n        found: validOptions,\n        wanted: 'number[]',\n      },\n    })\n  }\n  const validate =\n    val ?\n      (val as (v: unknown) => v is ValidValue<'number', false>)\n    : undefined\n  return {\n    ...rest,\n    default: def,\n    validate,\n    validOptions,\n    type: 'number',\n    multiple: false,\n  }\n}\n\nfunction numList(\n  o: ConfigOptionMeta<'number'> = {},\n): ConfigOptionBase<'number', true> {\n  const { default: def, validate: val, validOptions, ...rest } = o\n  if (def !== undefined && !isValidValue(def, 'number', true)) {\n    throw new TypeError('invalid default value', {\n      cause: {\n        found: def,\n        wanted: 'number[]',\n      },\n    })\n  }\n  if (!undefOrTypeArray(validOptions, 'number')) {\n    throw new TypeError('invalid validOptions', {\n      cause: {\n        found: validOptions,\n        wanted: 'number[]',\n      },\n    })\n  }\n  const validate =\n    val ?\n      (val as (v: unknown) => v is ValidValue<'number', true>)\n    : undefined\n  return {\n    ...rest,\n    default: def,\n    validate,\n    validOptions,\n    type: 'number',\n    multiple: true,\n  }\n}\n\nfunction opt(\n  o: ConfigOptionMeta<'string', false> = {},\n): ConfigOptionBase<'string', false> {\n  const { default: def, validate: val, validOptions, ...rest } = o\n  if (def !== undefined && !isValidValue(def, 'string', false)) {\n    throw new TypeError('invalid default value', {\n      cause: {\n        found: def,\n        wanted: 'string',\n      },\n    })\n  }\n  if (!undefOrTypeArray(validOptions, 'string')) {\n    throw new TypeError('invalid validOptions', {\n      cause: {\n        found: validOptions,\n        wanted: 'string[]',\n      },\n    })\n  }\n  const validate =\n    val ?\n      (val as (v: unknown) => v is ValidValue<'string', false>)\n    : undefined\n  return {\n    ...rest,\n    default: def,\n    validate,\n    validOptions,\n    type: 'string',\n    multiple: false,\n  }\n}\n\nfunction optList(\n  o: ConfigOptionMeta<'string'> = {},\n): ConfigOptionBase<'string', true> {\n  const { default: def, validate: val, validOptions, ...rest } = o\n  if (def !== undefined && !isValidValue(def, 'string', true)) {\n    throw new TypeError('invalid default value', {\n      cause: {\n        found: def,\n        wanted: 'string[]',\n      },\n    })\n  }\n  if (!undefOrTypeArray(validOptions, 'string')) {\n    throw new TypeError('invalid validOptions', {\n      cause: {\n        found: validOptions,\n        wanted: 'string[]',\n      },\n    })\n  }\n  const validate =\n    val ?\n      (val as (v: unknown) => v is ValidValue<'string', true>)\n    : undefined\n  return {\n    ...rest,\n    default: def,\n    validate,\n    validOptions,\n    type: 'string',\n    multiple: true,\n  }\n}\n\nfunction flag(\n  o: ConfigOptionMeta<'boolean', false> = {},\n): ConfigOptionBase<'boolean', false> {\n  const {\n    hint,\n    default: def,\n    validate: val,\n    ...rest\n  } = o as ConfigOptionMeta<'boolean', false>\n  delete (rest as ConfigOptionMeta<'string', false>).validOptions\n  if (def !== undefined && !isValidValue(def, 'boolean', false)) {\n    throw new TypeError('invalid default value')\n  }\n  const validate =\n    val ?\n      (val as (v: unknown) => v is ValidValue<'boolean', false>)\n    : undefined\n  if (hint !== undefined) {\n    throw new TypeError('cannot provide hint for flag')\n  }\n  return {\n    ...rest,\n    default: def,\n    validate,\n    type: 'boolean',\n    multiple: false,\n  }\n}\n\nfunction flagList(\n  o: ConfigOptionMeta<'boolean'> = {},\n): ConfigOptionBase<'boolean', true> {\n  const {\n    hint,\n    default: def,\n    validate: val,\n    ...rest\n  } = o as ConfigOptionMeta<'boolean', false>\n  delete (rest as ConfigOptionMeta<'string', false>).validOptions\n  if (def !== undefined && !isValidValue(def, 'boolean', true)) {\n    throw new TypeError('invalid default value')\n  }\n  const validate =\n    val ?\n      (val as (v: unknown) => v is ValidValue<'boolean', true>)\n    : undefined\n  if (hint !== undefined) {\n    throw new TypeError('cannot provide hint for flag list')\n  }\n  return {\n    ...rest,\n    default: def,\n    validate,\n    type: 'boolean',\n    multiple: true,\n  }\n}\nconst toParseArgsOptionsConfig = (\n  options: ConfigSet,\n): Exclude<ParseArgsConfig['options'], undefined> => {\n  const c: Exclude<ParseArgsConfig['options'], undefined> = {}\n  for (const longOption in options) {\n    const config = options[longOption]\n    /* c8 ignore start */\n    if (!config) {\n      throw new Error('config must be an object: ' + longOption)\n    }\n    /* c8 ignore start */\n    if (isConfigOption(config, 'number', true)) {\n      c[longOption] = {\n        type: 'string',\n        multiple: true,\n        default: config.default?.map(c => String(c)),\n      }\n    } else if (isConfigOption(config, 'number', false)) {\n      c[longOption] = {\n        type: 'string',\n        multiple: false,\n        default:\n          config.default === undefined ?\n            undefined\n          : String(config.default),\n      }\n    } else {\n      const conf = config as\n        | ConfigOptionBase<'string'>\n        | ConfigOptionBase<'boolean'>\n      c[longOption] = {\n        type: conf.type,\n        multiple: !!conf.multiple,\n        default: conf.default,\n      }\n    }\n    const clo = c[longOption] as ConfigOptionBase<ConfigType>\n    if (typeof config.short === 'string') {\n      clo.short = config.short\n    }\n\n    if (\n      config.type === 'boolean' &&\n      !longOption.startsWith('no-') &&\n      !options[`no-${longOption}`]\n    ) {\n      c[`no-${longOption}`] = {\n        type: 'boolean',\n        multiple: config.multiple,\n      }\n    }\n  }\n  return c\n}\n\n/**\n * A row used when generating the {@link Jack#usage} string\n */\nexport interface Row {\n  left?: string\n  text: string\n  skipLine?: boolean\n  type?: string\n}\n\n/**\n * A heading for a section in the usage, created by the jack.heading()\n * method.\n *\n * First heading is always level 1, subsequent headings default to 2.\n *\n * The level of the nearest heading level sets the indentation of the\n * description that follows.\n */\nexport interface Heading extends Row {\n  type: 'heading'\n  text: string\n  left?: ''\n  skipLine?: boolean\n  level: number\n  pre?: boolean\n}\nconst isHeading = (r: { type?: string }): r is Heading =>\n  r.type === 'heading'\n\n/**\n * An arbitrary blob of text describing some stuff, set by the\n * jack.description() method.\n *\n * Indentation determined by level of the nearest header.\n */\nexport interface Description extends Row {\n  type: 'description'\n  text: string\n  left?: ''\n  skipLine?: boolean\n  pre?: boolean\n}\n\nconst isDescription = (r: { type?: string }): r is Description =>\n  r.type === 'description'\n\n/**\n * A heading or description row used when generating the {@link Jack#usage}\n * string\n */\nexport type TextRow = Heading | Description\n\n/**\n * Either a {@link TextRow} or a reference to a {@link ConfigOptionBase}\n */\nexport type UsageField =\n  | TextRow\n  | {\n      type: 'config'\n      name: string\n      value: ConfigOptionBase<ConfigType>\n    }\n\n/**\n * Options provided to the {@link Jack} constructor\n */\nexport interface JackOptions {\n  /**\n   * Whether to allow positional arguments\n   *\n   * @default true\n   */\n  allowPositionals?: boolean\n\n  /**\n   * Prefix to use when reading/writing the environment variables\n   *\n   * If not specified, environment behavior will not be available.\n   */\n  envPrefix?: string\n\n  /**\n   * Environment object to read/write. Defaults `process.env`.\n   * No effect if `envPrefix` is not set.\n   */\n  env?: { [k: string]: string | undefined }\n\n  /**\n   * A short usage string. If not provided, will be generated from the\n   * options provided, but that can of course be rather verbose if\n   * there are a lot of options.\n   */\n  usage?: string\n\n  /**\n   * Stop parsing flags and opts at the first positional argument.\n   * This is to support cases like `cmd [flags] <subcmd> [options]`, where\n   * each subcommand may have different options.  This effectively treats\n   * any positional as a `--` argument.  Only relevant if `allowPositionals`\n   * is true.\n   *\n   * To do subcommands, set this option, look at the first positional, and\n   * parse the remaining positionals as appropriate.\n   *\n   * @default false\n   */\n  stopAtPositional?: boolean\n\n  /**\n   * Conditional `stopAtPositional`. If set to a `(string)=>boolean` function,\n   * will be called with each positional argument encountered. If the function\n   * returns true, then parsing will stop at that point.\n   */\n  stopAtPositionalTest?: (arg: string) => boolean\n}\n\n/**\n * Class returned by the {@link jack} function and all configuration\n * definition methods.  This is what gets chained together.\n */\nexport class Jack<C extends ConfigSet = {}> {\n  #configSet: C\n  #shorts: { [k: string]: string }\n  #options: JackOptions\n  #fields: UsageField[] = []\n  #env: { [k: string]: string | undefined }\n  #envPrefix?: string\n  #allowPositionals: boolean\n  #usage?: string\n  #usageMarkdown?: string\n\n  constructor(options: JackOptions = {}) {\n    this.#options = options\n    this.#allowPositionals = options.allowPositionals !== false\n    this.#env =\n      this.#options.env === undefined ? process.env : this.#options.env\n    this.#envPrefix = options.envPrefix\n    // We need to fib a little, because it's always the same object, but it\n    // starts out as having an empty config set.  Then each method that adds\n    // fields returns `this as Jack<C & { ...newConfigs }>`\n    this.#configSet = Object.create(null) as C\n    this.#shorts = Object.create(null)\n  }\n\n  /**\n   * Set the default value (which will still be overridden by env or cli)\n   * as if from a parsed config file. The optional `source` param, if\n   * provided, will be included in error messages if a value is invalid or\n   * unknown.\n   */\n  setConfigValues(values: OptionsResults<C>, source = '') {\n    try {\n      this.validate(values)\n    } catch (er) {\n      const e = er as Error\n      if (source && e && typeof e === 'object') {\n        if (e.cause && typeof e.cause === 'object') {\n          Object.assign(e.cause, { path: source })\n        } else {\n          e.cause = { path: source }\n        }\n      }\n      throw e\n    }\n    for (const [field, value] of Object.entries(values)) {\n      const my = this.#configSet[field]\n      // already validated, just for TS's benefit\n      /* c8 ignore start */\n      if (!my) {\n        throw new Error('unexpected field in config set: ' + field, {\n          cause: { found: field },\n        })\n      }\n      /* c8 ignore stop */\n      my.default = value\n    }\n    return this\n  }\n\n  /**\n   * Parse a string of arguments, and return the resulting\n   * `{ values, positionals }` object.\n   *\n   * If an {@link JackOptions#envPrefix} is set, then it will read default\n   * values from the environment, and write the resulting values back\n   * to the environment as well.\n   *\n   * Environment values always take precedence over any other value, except\n   * an explicit CLI setting.\n   */\n  parse(args: string[] = process.argv): Parsed<C> {\n    this.loadEnvDefaults()\n    const p = this.parseRaw(args)\n    this.applyDefaults(p)\n    this.writeEnv(p)\n    return p\n  }\n\n  loadEnvDefaults() {\n    if (this.#envPrefix) {\n      for (const [field, my] of Object.entries(this.#configSet)) {\n        const ek = toEnvKey(this.#envPrefix, field)\n        const env = this.#env[ek]\n        if (env !== undefined) {\n          my.default = fromEnvVal(env, my.type, !!my.multiple, my.delim)\n        }\n      }\n    }\n  }\n\n  applyDefaults(p: Parsed<C>) {\n    for (const [field, c] of Object.entries(this.#configSet)) {\n      if (c.default !== undefined && !(field in p.values)) {\n        //@ts-ignore\n        p.values[field] = c.default\n      }\n    }\n  }\n\n  /**\n   * Only parse the command line arguments passed in.\n   * Does not strip off the `node script.js` bits, so it must be just the\n   * arguments you wish to have parsed.\n   * Does not read from or write to the environment, or set defaults.\n   */\n  parseRaw(args: string[]): Parsed<C> {\n    if (args === process.argv) {\n      args = args.slice(\n        (process as { _eval?: string })._eval !== undefined ? 1 : 2,\n      )\n    }\n\n    const options = toParseArgsOptionsConfig(this.#configSet)\n    const result = parseArgs({\n      args,\n      options,\n      // always strict, but using our own logic\n      strict: false,\n      allowPositionals: this.#allowPositionals,\n      tokens: true,\n    })\n\n    const p: Parsed<C> = {\n      values: {},\n      positionals: [],\n    }\n    for (const token of result.tokens) {\n      if (token.kind === 'positional') {\n        p.positionals.push(token.value)\n        if (\n          this.#options.stopAtPositional ||\n          this.#options.stopAtPositionalTest?.(token.value)\n        ) {\n          p.positionals.push(...args.slice(token.index + 1))\n          break\n        }\n      } else if (token.kind === 'option') {\n        let value: string | number | boolean | undefined = undefined\n        if (token.name.startsWith('no-')) {\n          const my = this.#configSet[token.name]\n          const pname = token.name.substring('no-'.length)\n          const pos = this.#configSet[pname]\n          if (\n            pos &&\n            pos.type === 'boolean' &&\n            (!my ||\n              (my.type === 'boolean' && !!my.multiple === !!pos.multiple))\n          ) {\n            value = false\n            token.name = pname\n          }\n        }\n        const my = this.#configSet[token.name]\n        if (!my) {\n          throw new Error(\n            `Unknown option '${token.rawName}'. ` +\n              `To specify a positional argument starting with a '-', ` +\n              `place it at the end of the command after '--', as in ` +\n              `'-- ${token.rawName}'`,\n            {\n              cause: {\n                found:\n                  token.rawName + (token.value ? `=${token.value}` : ''),\n              },\n            },\n          )\n        }\n        if (value === undefined) {\n          if (token.value === undefined) {\n            if (my.type !== 'boolean') {\n              throw new Error(\n                `No value provided for ${token.rawName}, expected ${my.type}`,\n                {\n                  cause: {\n                    name: token.rawName,\n                    wanted: valueType(my),\n                  },\n                },\n              )\n            }\n            value = true\n          } else {\n            if (my.type === 'boolean') {\n              throw new Error(\n                `Flag ${token.rawName} does not take a value, received '${token.value}'`,\n                { cause: { found: token } },\n              )\n            }\n            if (my.type === 'string') {\n              value = token.value\n            } else {\n              value = +token.value\n              if (value !== value) {\n                throw new Error(\n                  `Invalid value '${token.value}' provided for ` +\n                    `'${token.rawName}' option, expected number`,\n                  {\n                    cause: {\n                      name: token.rawName,\n                      found: token.value,\n                      wanted: 'number',\n                    },\n                  },\n                )\n              }\n            }\n          }\n        }\n        if (my.multiple) {\n          const pv = p.values as {\n            [k: string]: (string | number | boolean)[]\n          }\n          const tn = pv[token.name] ?? []\n          pv[token.name] = tn\n          tn.push(value)\n        } else {\n          const pv = p.values as { [k: string]: string | number | boolean }\n          pv[token.name] = value\n        }\n      }\n    }\n\n    for (const [field, value] of Object.entries(p.values)) {\n      const valid = this.#configSet[field]?.validate\n      const validOptions = this.#configSet[field]?.validOptions\n      let cause:\n        | undefined\n        | {\n            name: string\n            found: unknown\n            validOptions?: readonly string[] | readonly number[]\n          }\n      if (validOptions && !isValidOption(value, validOptions)) {\n        cause = { name: field, found: value, validOptions: validOptions }\n      }\n      if (valid && !valid(value)) {\n        cause = cause || { name: field, found: value }\n      }\n      if (cause) {\n        throw new Error(\n          `Invalid value provided for --${field}: ${JSON.stringify(\n            value,\n          )}`,\n          { cause },\n        )\n      }\n    }\n\n    return p\n  }\n\n  /**\n   * do not set fields as 'no-foo' if 'foo' exists and both are bools\n   * just set foo.\n   */\n  #noNoFields(f: string, val: unknown, s: string = f) {\n    if (!f.startsWith('no-') || typeof val !== 'boolean') return\n    const yes = f.substring('no-'.length)\n    // recurse so we get the core config key we care about.\n    this.#noNoFields(yes, val, s)\n    if (this.#configSet[yes]?.type === 'boolean') {\n      throw new Error(\n        `do not set '${s}', instead set '${yes}' as desired.`,\n        { cause: { found: s, wanted: yes } },\n      )\n    }\n  }\n\n  /**\n   * Validate that any arbitrary object is a valid configuration `values`\n   * object.  Useful when loading config files or other sources.\n   */\n  validate(o: unknown): asserts o is Parsed<C>['values'] {\n    if (!o || typeof o !== 'object') {\n      throw new Error('Invalid config: not an object', {\n        cause: { found: o },\n      })\n    }\n    const opts = o as Record<string, ValidValue>\n    for (const field in o) {\n      const value = opts[field]\n      /* c8 ignore next - for TS */\n      if (value === undefined) continue\n      this.#noNoFields(field, value)\n      const config = this.#configSet[field]\n      if (!config) {\n        throw new Error(`Unknown config option: ${field}`, {\n          cause: { found: field },\n        })\n      }\n      if (!isValidValue(value, config.type, !!config.multiple)) {\n        throw new Error(\n          `Invalid value ${valueType(\n            value,\n          )} for ${field}, expected ${valueType(config)}`,\n          {\n            cause: {\n              name: field,\n              found: value,\n              wanted: valueType(config),\n            },\n          },\n        )\n      }\n      let cause:\n        | undefined\n        | {\n            name: string\n            found: any\n            validOptions?: readonly string[] | readonly number[]\n          }\n      if (\n        config.validOptions &&\n        !isValidOption(value, config.validOptions)\n      ) {\n        cause = {\n          name: field,\n          found: value,\n          validOptions: config.validOptions,\n        }\n      }\n      if (config.validate && !config.validate(value)) {\n        cause = cause || { name: field, found: value }\n      }\n      if (cause) {\n        throw new Error(`Invalid config value for ${field}: ${value}`, {\n          cause,\n        })\n      }\n    }\n  }\n\n  writeEnv(p: Parsed<C>) {\n    if (!this.#env || !this.#envPrefix) return\n    for (const [field, value] of Object.entries(p.values)) {\n      const my = this.#configSet[field]\n      this.#env[toEnvKey(this.#envPrefix, field)] = toEnvVal(\n        value,\n        my?.delim,\n      )\n    }\n  }\n\n  /**\n   * Add a heading to the usage output banner\n   */\n  heading(\n    text: string,\n    level?: 1 | 2 | 3 | 4 | 5 | 6,\n    { pre = false }: { pre?: boolean } = {},\n  ): Jack<C> {\n    if (level === undefined) {\n      level = this.#fields.some(r => isHeading(r)) ? 2 : 1\n    }\n    this.#fields.push({ type: 'heading', text, level, pre })\n    return this\n  }\n\n  /**\n   * Add a long-form description to the usage output at this position.\n   */\n  description(text: string, { pre }: { pre?: boolean } = {}): Jack<C> {\n    this.#fields.push({ type: 'description', text, pre })\n    return this\n  }\n\n  /**\n   * Add one or more number fields.\n   */\n  num<F extends ConfigMetaSet<'number', false>>(\n    fields: F,\n  ): Jack<C & ConfigSetFromMetaSet<'number', false, F>> {\n    return this.#addFields(fields, num)\n  }\n\n  /**\n   * Add one or more multiple number fields.\n   */\n  numList<F extends ConfigMetaSet<'number'>>(\n    fields: F,\n  ): Jack<C & ConfigSetFromMetaSet<'number', true, F>> {\n    return this.#addFields(fields, numList)\n  }\n\n  /**\n   * Add one or more string option fields.\n   */\n  opt<F extends ConfigMetaSet<'string', false>>(\n    fields: F,\n  ): Jack<C & ConfigSetFromMetaSet<'string', false, F>> {\n    return this.#addFields(fields, opt)\n  }\n\n  /**\n   * Add one or more multiple string option fields.\n   */\n  optList<F extends ConfigMetaSet<'string'>>(\n    fields: F,\n  ): Jack<C & ConfigSetFromMetaSet<'string', true, F>> {\n    return this.#addFields(fields, optList)\n  }\n\n  /**\n   * Add one or more flag fields.\n   */\n  flag<F extends ConfigMetaSet<'boolean', false>>(\n    fields: F,\n  ): Jack<C & ConfigSetFromMetaSet<'boolean', false, F>> {\n    return this.#addFields(fields, flag)\n  }\n\n  /**\n   * Add one or more multiple flag fields.\n   */\n  flagList<F extends ConfigMetaSet<'boolean'>>(\n    fields: F,\n  ): Jack<C & ConfigSetFromMetaSet<'boolean', true, F>> {\n    return this.#addFields(fields, flagList)\n  }\n\n  /**\n   * Generic field definition method. Similar to flag/flagList/number/etc,\n   * but you must specify the `type` (and optionally `multiple` and `delim`)\n   * fields on each one, or Jack won't know how to define them.\n   */\n  addFields<F extends ConfigSet>(fields: F): Jack<C & F> {\n    const next = this as unknown as Jack<C & F>\n    for (const [name, field] of Object.entries(fields)) {\n      this.#validateName(name, field)\n      next.#fields.push({\n        type: 'config',\n        name,\n        value: field as ConfigOptionBase<ConfigType>,\n      })\n    }\n    Object.assign(next.#configSet, fields)\n    return next\n  }\n\n  #addFields<\n    T extends ConfigType,\n    M extends boolean,\n    F extends ConfigMetaSet<T, M>,\n  >(\n    fields: F,\n    fn: (m: ConfigOptionMeta<T, M>) => ConfigOptionBase<T, M>,\n  ): Jack<C & ConfigSetFromMetaSet<T, M, F>> {\n    type NextC = C & ConfigSetFromMetaSet<T, M, F>\n    const next = this as unknown as Jack<NextC>\n    Object.assign(\n      next.#configSet,\n      Object.fromEntries(\n        Object.entries(fields).map(([name, field]) => {\n          this.#validateName(name, field)\n          const option = fn(field)\n          next.#fields.push({\n            type: 'config',\n            name,\n            value: option as ConfigOptionBase<ConfigType>,\n          })\n          return [name, option]\n        }),\n      ),\n    )\n    return next\n  }\n\n  #validateName(name: string, field: { short?: string }) {\n    if (!/^[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?$/.test(name)) {\n      throw new TypeError(\n        `Invalid option name: ${name}, ` +\n          `must be '-' delimited ASCII alphanumeric`,\n      )\n    }\n    if (this.#configSet[name]) {\n      throw new TypeError(`Cannot redefine option ${field}`)\n    }\n    if (this.#shorts[name]) {\n      throw new TypeError(\n        `Cannot redefine option ${name}, already ` +\n          `in use for ${this.#shorts[name]}`,\n      )\n    }\n    if (field.short) {\n      if (!/^[a-zA-Z0-9]$/.test(field.short)) {\n        throw new TypeError(\n          `Invalid ${name} short option: ${field.short}, ` +\n            'must be 1 ASCII alphanumeric character',\n        )\n      }\n      if (this.#shorts[field.short]) {\n        throw new TypeError(\n          `Invalid ${name} short option: ${field.short}, ` +\n            `already in use for ${this.#shorts[field.short]}`,\n        )\n      }\n      this.#shorts[field.short] = name\n      this.#shorts[name] = name\n    }\n  }\n\n  /**\n   * Return the usage banner for the given configuration\n   */\n  usage(): string {\n    if (this.#usage) return this.#usage\n\n    let headingLevel = 1\n    const ui = cliui({ width })\n    const first = this.#fields[0]\n    let start = first?.type === 'heading' ? 1 : 0\n    if (first?.type === 'heading') {\n      ui.div({\n        padding: [0, 0, 0, 0],\n        text: normalize(first.text),\n      })\n    }\n    ui.div({ padding: [0, 0, 0, 0], text: 'Usage:' })\n    if (this.#options.usage) {\n      ui.div({\n        text: this.#options.usage,\n        padding: [0, 0, 0, 2],\n      })\n    } else {\n      const cmd = basename(String(process.argv[1]))\n      const shortFlags: string[] = []\n      const shorts: string[][] = []\n      const flags: string[] = []\n      const opts: string[][] = []\n      for (const [field, config] of Object.entries(this.#configSet)) {\n        if (config.short) {\n          if (config.type === 'boolean') shortFlags.push(config.short)\n          else shorts.push([config.short, config.hint || field])\n        } else {\n          if (config.type === 'boolean') flags.push(field)\n          else opts.push([field, config.hint || field])\n        }\n      }\n      const sf = shortFlags.length ? ' -' + shortFlags.join('') : ''\n      const so = shorts.map(([k, v]) => ` --${k}=<${v}>`).join('')\n      const lf = flags.map(k => ` --${k}`).join('')\n      const lo = opts.map(([k, v]) => ` --${k}=<${v}>`).join('')\n      const usage = `${cmd}${sf}${so}${lf}${lo}`.trim()\n      ui.div({\n        text: usage,\n        padding: [0, 0, 0, 2],\n      })\n    }\n\n    ui.div({ padding: [0, 0, 0, 0], text: '' })\n    const maybeDesc = this.#fields[start]\n    if (maybeDesc && isDescription(maybeDesc)) {\n      const print = normalize(maybeDesc.text, maybeDesc.pre)\n      start++\n      ui.div({ padding: [0, 0, 0, 0], text: print })\n      ui.div({ padding: [0, 0, 0, 0], text: '' })\n    }\n\n    const { rows, maxWidth } = this.#usageRows(start)\n\n    // every heading/description after the first gets indented by 2\n    // extra spaces.\n    for (const row of rows) {\n      if (row.left) {\n        // If the row is too long, don't wrap it\n        // Bump the right-hand side down a line to make room\n        const configIndent = indent(Math.max(headingLevel, 2))\n        if (row.left.length > maxWidth - 3) {\n          ui.div({ text: row.left, padding: [0, 0, 0, configIndent] })\n          ui.div({ text: row.text, padding: [0, 0, 0, maxWidth] })\n        } else {\n          ui.div(\n            {\n              text: row.left,\n              padding: [0, 1, 0, configIndent],\n              width: maxWidth,\n            },\n            { padding: [0, 0, 0, 0], text: row.text },\n          )\n        }\n        if (row.skipLine) {\n          ui.div({ padding: [0, 0, 0, 0], text: '' })\n        }\n      } else {\n        if (isHeading(row)) {\n          const { level } = row\n          headingLevel = level\n          // only h1 and h2 have bottom padding\n          // h3-h6 do not\n          const b = level <= 2 ? 1 : 0\n          ui.div({ ...row, padding: [0, 0, b, indent(level)] })\n        } else {\n          ui.div({ ...row, padding: [0, 0, 1, indent(headingLevel + 1)] })\n        }\n      }\n    }\n\n    return (this.#usage = ui.toString())\n  }\n\n  /**\n   * Return the usage banner markdown for the given configuration\n   */\n  usageMarkdown(): string {\n    if (this.#usageMarkdown) return this.#usageMarkdown\n\n    const out: string[] = []\n\n    let headingLevel = 1\n    const first = this.#fields[0]\n    let start = first?.type === 'heading' ? 1 : 0\n    if (first?.type === 'heading') {\n      out.push(`# ${normalizeOneLine(first.text)}`)\n    }\n    out.push('Usage:')\n    if (this.#options.usage) {\n      out.push(normalizeMarkdown(this.#options.usage, true))\n    } else {\n      const cmd = basename(String(process.argv[1]))\n      const shortFlags: string[] = []\n      const shorts: string[][] = []\n      const flags: string[] = []\n      const opts: string[][] = []\n      for (const [field, config] of Object.entries(this.#configSet)) {\n        if (config.short) {\n          if (config.type === 'boolean') shortFlags.push(config.short)\n          else shorts.push([config.short, config.hint || field])\n        } else {\n          if (config.type === 'boolean') flags.push(field)\n          else opts.push([field, config.hint || field])\n        }\n      }\n      const sf = shortFlags.length ? ' -' + shortFlags.join('') : ''\n      const so = shorts.map(([k, v]) => ` --${k}=<${v}>`).join('')\n      const lf = flags.map(k => ` --${k}`).join('')\n      const lo = opts.map(([k, v]) => ` --${k}=<${v}>`).join('')\n      const usage = `${cmd}${sf}${so}${lf}${lo}`.trim()\n      out.push(normalizeMarkdown(usage, true))\n    }\n\n    const maybeDesc = this.#fields[start]\n    if (maybeDesc && isDescription(maybeDesc)) {\n      out.push(normalizeMarkdown(maybeDesc.text, maybeDesc.pre))\n      start++\n    }\n\n    const { rows } = this.#usageRows(start)\n\n    // heading level in markdown is number of # ahead of text\n    for (const row of rows) {\n      if (row.left) {\n        out.push(\n          '#'.repeat(headingLevel + 1) +\n            ' ' +\n            normalizeOneLine(row.left, true),\n        )\n        if (row.text) out.push(normalizeMarkdown(row.text))\n      } else if (isHeading(row)) {\n        const { level } = row\n        headingLevel = level\n        out.push(\n          `${'#'.repeat(headingLevel)} ${normalizeOneLine(\n            row.text,\n            row.pre,\n          )}`,\n        )\n      } else {\n        out.push(normalizeMarkdown(row.text, !!(row as Description).pre))\n      }\n    }\n\n    return (this.#usageMarkdown = out.join('\\n\\n') + '\\n')\n  }\n\n  #usageRows(start: number) {\n    // turn each config type into a row, and figure out the width of the\n    // left hand indentation for the option descriptions.\n    let maxMax = Math.max(12, Math.min(26, Math.floor(width / 3)))\n    let maxWidth = 8\n    let prev: Row | TextRow | undefined = undefined\n    const rows: (Row | TextRow)[] = []\n    for (const field of this.#fields.slice(start)) {\n      if (field.type !== 'config') {\n        if (prev?.type === 'config') prev.skipLine = true\n        prev = undefined\n        field.text = normalize(field.text, !!field.pre)\n        rows.push(field)\n        continue\n      }\n      const { value } = field\n      const desc = value.description || ''\n      const mult = value.multiple ? 'Can be set multiple times' : ''\n      const opts =\n        value.validOptions?.length ?\n          `Valid options:${value.validOptions.map(\n            v => ` ${JSON.stringify(v)}`,\n          )}`\n        : ''\n      const dmDelim = desc.includes('\\n') ? '\\n\\n' : '\\n'\n      const extra = [opts, mult].join(dmDelim).trim()\n      const text = (normalize(desc) + dmDelim + extra).trim()\n      const hint =\n        value.hint ||\n        (value.type === 'number' ? 'n'\n        : value.type === 'string' ? field.name\n        : undefined)\n      const short =\n        !value.short ? ''\n        : value.type === 'boolean' ? `-${value.short} `\n        : `-${value.short}<${hint}> `\n      const left =\n        value.type === 'boolean' ?\n          `${short}--${field.name}`\n        : `${short}--${field.name}=<${hint}>`\n      const row: Row = { text, left, type: 'config' }\n      if (text.length > width - maxMax) {\n        row.skipLine = true\n      }\n      if (prev && left.length > maxMax) prev.skipLine = true\n      prev = row\n      const len = left.length + 4\n      if (len > maxWidth && len < maxMax) {\n        maxWidth = len\n      }\n\n      rows.push(row)\n    }\n\n    return { rows, maxWidth }\n  }\n\n  /**\n   * Return the configuration options as a plain object\n   */\n  toJSON() {\n    return Object.fromEntries(\n      Object.entries(this.#configSet).map(([field, def]) => [\n        field,\n        {\n          type: def.type,\n          ...(def.multiple ? { multiple: true } : {}),\n          ...(def.delim ? { delim: def.delim } : {}),\n          ...(def.short ? { short: def.short } : {}),\n          ...(def.description ?\n            { description: normalize(def.description) }\n          : {}),\n          ...(def.validate ? { validate: def.validate } : {}),\n          ...(def.validOptions ? { validOptions: def.validOptions } : {}),\n          ...(def.default !== undefined ? { default: def.default } : {}),\n          ...(def.hint ? { hint: def.hint } : {}),\n        },\n      ]),\n    )\n  }\n\n  /**\n   * Custom printer for `util.inspect`\n   */\n  [inspect.custom](_: number, options: InspectOptions) {\n    return `Jack ${inspect(this.toJSON(), options)}`\n  }\n}\n\n// Unwrap and un-indent, so we can wrap description\n// strings however makes them look nice in the code.\nconst normalize = (s: string, pre = false) => {\n  if (pre)\n    // prepend a ZWSP to each line so cliui doesn't strip it.\n    return s\n      .split('\\n')\n      .map(l => `\\u200b${l}`)\n      .join('\\n')\n  return s\n    .split(/^\\s*```\\s*$/gm)\n    .map((s, i) => {\n      if (i % 2 === 1) {\n        if (!s.trim()) {\n          return `\\`\\`\\`\\n\\`\\`\\`\\n`\n        }\n        // outdent the ``` blocks, but preserve whitespace otherwise.\n        const split = s.split('\\n')\n        // throw out the \\n at the start and end\n        split.pop()\n        split.shift()\n        const si = split.reduce((shortest, l) => {\n          /* c8 ignore next */\n          const ind = l.match(/^\\s*/)?.[0] ?? ''\n          if (ind.length) return Math.min(ind.length, shortest)\n          else return shortest\n        }, Infinity)\n        /* c8 ignore next */\n        const i = isFinite(si) ? si : 0\n        return (\n          '\\n```\\n' +\n          split.map(s => `\\u200b${s.substring(i)}`).join('\\n') +\n          '\\n```\\n'\n        )\n      }\n      return (\n        s\n          // remove single line breaks, except for lists\n          .replace(/([^\\n])\\n[ \\t]*([^\\n])/g, (_, $1, $2) =>\n            !/^[-*]/.test($2) ? `${$1} ${$2}` : `${$1}\\n${$2}`,\n          )\n          // normalize mid-line whitespace\n          .replace(/([^\\n])[ \\t]+([^\\n])/g, '$1 $2')\n          // two line breaks are enough\n          .replace(/\\n{3,}/g, '\\n\\n')\n          // remove any spaces at the start of a line\n          .replace(/\\n[ \\t]+/g, '\\n')\n          .trim()\n      )\n    })\n    .join('\\n')\n}\n\n// normalize for markdown printing, remove leading spaces on lines\nconst normalizeMarkdown = (s: string, pre: boolean = false): string => {\n  const n = normalize(s, pre).replace(/\\\\/g, '\\\\\\\\')\n  return pre ?\n      `\\`\\`\\`\\n${n.replace(/\\u200b/g, '')}\\n\\`\\`\\``\n    : n.replace(/\\n +/g, '\\n').trim()\n}\n\nconst normalizeOneLine = (s: string, pre: boolean = false) => {\n  const n = normalize(s, pre)\n    .replace(/[\\s\\u200b]+/g, ' ')\n    .trim()\n  return pre ? `\\`${n}\\`` : n\n}\n\n/**\n * Main entry point. Create and return a {@link Jack} object.\n */\nexport const jack = (options: JackOptions = {}) => new Jack(options)\n"]}